'use client'

import { useEffect } from 'react';
import { Loader2 } from 'lucide-react';

export default function HomePage() {
  useEffect(() => {
    console.log('🏠 HomePage: Auth disabled - redirecting directly to dashboard');
    // Auth disabled for testing - redirect directly to dashboard
    window.location.href = '/dashboard';
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="flex items-center gap-2">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span>Redirecting to dashboard...</span>
      </div>
    </div>
  );
}
