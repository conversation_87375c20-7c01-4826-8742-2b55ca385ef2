import { NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import { IndexingService } from '@/lib/storage/indexingService';
import path from 'path';

const execAsync = promisify(exec);
const indexingService = IndexingService.getInstance();

// Add timeout wrapper for exec commands
const execWithTimeout = async (command: string, timeoutMs: number = 10000) => {
  return Promise.race([
    execAsync(command),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error(`Command timeout after ${timeoutMs}ms`)), timeoutMs)
    )
  ]) as Promise<{ stdout: string; stderr: string }>;
};

export async function POST() {
  const startTime = Date.now();

  try {
    console.log('🚀 FAST REFRESH: Starting optimized refresh...');

    const storagePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';

    // Step 1: Quick scan for recent files (OPTIMIZED - 2-3 seconds max)
    console.log('⚡ Step 1: Quick scan for recent files...');

    const currentYear = new Date().getFullYear();

    // OPTIMIZED: Only search current year and limit to 500 files max
    // This prevents the command from taking too long on large directories
    const findCommand = `find "${storagePath}/${currentYear}" -type f \\( ` +
      `-name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" ` +
      `-o -name "*.mp4" -o -name "*.mov" -o -name "*.avi" -o -name "*.mkv" ` +
      `-o -name "*.mp3" -o -name "*.wav" -o -name "*.flac" ` +
      `-o -name "*.pdf" -o -name "*.txt" -o -name "*.doc" -o -name "*.docx" ` +
      `\\) -mtime -3 2>/dev/null | head -500`;

    console.log(`🔍 Searching for files modified in last 3 days (current year only)...`);

    // Use timeout to prevent hanging
    const { stdout: recentFiles } = await execWithTimeout(findCommand, 8000);
    let newFilePaths = recentFiles.trim().split('\n').filter(f => f.length > 0);

    console.log(`⚡ Found ${newFilePaths.length} recent files in ${Date.now() - startTime}ms`);

    // If no files found in current year, check if we have any test files we created
    if (newFilePaths.length === 0) {
      console.log('🔄 No recent files found, checking for any new test files...');
      const testCommand = `find "${storagePath}" -name "*test*" -type f -mtime -1 2>/dev/null | head -10`;
      try {
        const { stdout: testFiles } = await execWithTimeout(testCommand, 3000);
        newFilePaths = testFiles.trim().split('\n').filter(f => f.length > 0);
        console.log(`⚡ Found ${newFilePaths.length} test files`);
      } catch (error) {
        console.log('⚠️ Test file search failed, continuing...');
      }
    }

    // Debug: Show some of the files found
    if (newFilePaths.length > 0) {
      console.log(`🔍 DEBUG: First 3 files found:`, newFilePaths.slice(0, 3));
    }

    if (newFilePaths.length === 0) {
      console.log('✅ No new files found - performing quick cleanup...');

      // Quick cleanup for deleted files
      let deletedAssets = 0;
      try {
        const cleanupResults = await indexingService.quickCleanupCheck();
        deletedAssets = cleanupResults.deletedCount || 0;
        console.log(`🧹 Quick cleanup: ${deletedAssets} deleted files removed`);
      } catch (error) {
        console.log('⚠️ Quick cleanup failed, skipping...');
      }

      return NextResponse.json({
        success: true,
        message: `No new files found. ${deletedAssets > 0 ? `Cleaned up ${deletedAssets} deleted files.` : 'Database is up to date.'}`,
        refreshResults: {
          newAssets: 0,
          updatedAssets: 0,
          deletedAssets,
          foldersChecked: 1,
          duration: Date.now() - startTime,
          method: 'optimized-fast'
        }
      });
    }
    
    // Step 2: Quick database check for new files (OPTIMIZED - under 1 second)
    console.log('⚡ Step 2: Quick database check for new files...');

    const { db } = await import('@/lib/db/schema');
    const { assets } = await import('@/lib/db/schema');
    const { inArray } = await import('drizzle-orm');

    // Convert absolute paths to relative paths for database comparison
    const relativePaths = newFilePaths.slice(0, 100).map(filePath =>
      path.relative(storagePath, filePath)
    );

    console.log(`🔍 Checking ${relativePaths.length} files against database...`);

    // Batch check for existing files (limit to prevent slow queries)
    const existingAssets = await db.select({ filePath: assets.filePath })
      .from(assets)
      .where(inArray(assets.filePath, relativePaths));

    console.log(`🔍 Found ${existingAssets.length} existing assets in database`);

    const existingPaths = new Set(existingAssets.map(a => a.filePath));
    const trulyNewFiles = newFilePaths.slice(0, 100).filter(filePath => {
      const relativePath = path.relative(storagePath, filePath);
      return !existingPaths.has(relativePath);
    });

    console.log(`⚡ Found ${trulyNewFiles.length} truly new files that need indexing`);
    if (trulyNewFiles.length > 0) {
      console.log(`🔍 First 3 new files:`, trulyNewFiles.slice(0, 3).map(f => path.basename(f)));
    }
    
    // Step 3: Index new files (OPTIMIZED - limit to 20 files max)
    let newAssets = 0;
    const filesToIndex = trulyNewFiles.slice(0, 20); // Limit to prevent timeout

    if (filesToIndex.length > 0) {
      console.log(`⚡ Step 3: Indexing ${filesToIndex.length} new files...`);

      for (const filePath of filesToIndex) {
        try {
          await indexingService.indexFile(filePath);
          newAssets++;
          console.log(`✅ Indexed: ${path.basename(filePath)}`);
        } catch (error) {
          console.error(`❌ Error indexing ${path.basename(filePath)}:`, error);
        }
      }
    }

    // Step 4: Quick cleanup (OPTIMIZED - sample check only)
    console.log('🧹 Step 4: Quick cleanup check...');
    let deletedAssets = 0;
    try {
      // Use quick cleanup instead of full cleanup to save time
      const cleanupResults = await indexingService.quickCleanupCheck();
      deletedAssets = cleanupResults.deletedCount || 0;
      console.log(`🧹 Quick cleanup: ${deletedAssets} deleted files removed`);
    } catch (error) {
      console.log('⚠️ Cleanup skipped due to error:', error);
    }

    const duration = Date.now() - startTime;
    console.log(`✅ OPTIMIZED REFRESH completed in ${duration}ms: ${newAssets} new assets indexed, ${deletedAssets} deleted assets cleaned up`);

    return NextResponse.json({
      success: true,
      message: `Optimized refresh completed: ${newAssets} new assets found, ${deletedAssets} deleted files cleaned up`,
      refreshResults: {
        newAssets,
        updatedAssets: 0,
        deletedAssets,
        foldersChecked: 1,
        duration,
        method: 'optimized-fast',
        detectedFiles: filesToIndex.map(f => path.basename(f))
      }
    });
    
  } catch (error) {
    console.error('❌ Fast refresh error:', error);
    return NextResponse.json({
      success: false,
      error: 'Fast refresh failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

// GET endpoint to check what files would be found
export async function GET() {
  try {
    const storagePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
    
    // Just show what files would be found without indexing them (optimized search)
    const currentYear = new Date().getFullYear();
    const yearPath = `"${storagePath}/${currentYear}"`;

    const findCommand = `find ${yearPath} -maxdepth 4 -type f \\( ` +
      // Images
      `-name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.svg" -o -name "*.webp" -o -name "*.bmp" -o -name "*.tiff" -o -name "*.tif" -o -name "*.raw" -o -name "*.cr2" -o -name "*.nef" -o -name "*.arw" -o -name "*.dng" -o -name "*.heic" -o -name "*.heif" ` +
      // Videos
      `-o -name "*.mp4" -o -name "*.webm" -o -name "*.mov" -o -name "*.avi" -o -name "*.wmv" -o -name "*.flv" -o -name "*.mkv" -o -name "*.m4v" -o -name "*.3gp" -o -name "*.ogv" ` +
      // Audio
      `-o -name "*.mp3" -o -name "*.wav" -o -name "*.ogg" -o -name "*.aac" -o -name "*.m4a" -o -name "*.flac" -o -name "*.wma" -o -name "*.opus" ` +
      // Documents
      `-o -name "*.pdf" -o -name "*.doc" -o -name "*.docx" -o -name "*.xls" -o -name "*.xlsx" -o -name "*.ppt" -o -name "*.pptx" -o -name "*.txt" -o -name "*.csv" -o -name "*.rtf" -o -name "*.odt" -o -name "*.ods" -o -name "*.odp" ` +
      `\\) 2>/dev/null | head -20`;
    
    const { stdout: recentFiles } = await execAsync(findCommand);
    const filePaths = recentFiles.trim().split('\n').filter(f => f.length > 0);
    
    return NextResponse.json({
      success: true,
      message: `Found ${filePaths.length} files modified in last 7 days`,
      files: filePaths.map(f => path.basename(f)),
      note: 'This is a preview - use POST to actually index new files'
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Preview failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
