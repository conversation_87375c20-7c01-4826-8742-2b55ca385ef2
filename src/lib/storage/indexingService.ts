import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import exifr from 'exifr';
import chokidar, { FSWatcher } from 'chokidar';
import { db } from '@/lib/db/schema';
import { assets, tags, assetTags, folders } from '@/lib/db/schema';
import { eq, desc, and, or, sql, gt, like, exists } from 'drizzle-orm';
import { getFileType, sanitizeMetadata } from '@/lib/utils';
import { extractDateFromFolderPath, folderDateToDate } from '@/lib/utils/folderDateParser';
import { parseSearchQuery, buildSearchTerms, shouldUseAndLogic } from '@/lib/utils/searchParser';
import { FolderTrackingService } from './folderTrackingService';

export interface Asset {
  id: string;
  filename: string;
  filePath: string;
  fileUrl: string;
  thumbnailUrl: string;
  type: string;
  size: number;
  lastModified: Date;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export class IndexingService {
  private static instance: IndexingService;
  private isIndexing = false;
  private isFastIndexing = false;
  private lastIndexCheckedTime = 0;
  private minTimeBetweenIndexing: number = 30 * 1000; // 30 seconds
  private basePath = process.env.STORAGE_PATH || '/mnt/nas/photos/MASTER-PHOTOS';
  private totalFiles = 0;
  private processedFiles = 0;
  private watcher: FSWatcher | null = null;
  private isWatching = false;
  private watcherQueue: Set<string> = new Set();
  private watcherTimer: NodeJS.Timeout | null = null;
  private folderTracker: FolderTrackingService;

  // Enhanced progress tracking for modal display
  private currentOperation = '';
  private currentFolder = '';
  private foldersToCheck = 0;
  private foldersChecked = 0;
  private detectedChanges: string[] = [];

  private constructor() {
    console.log('🚀 IndexingService initialized - manual mode enabled');
    console.log('📋 Manual mode: No automatic file monitoring. Use manual controls to scan for changes.');
    this.folderTracker = FolderTrackingService.getInstance();
  }

  static getInstance(): IndexingService {
    if (!IndexingService.instance) {
      IndexingService.instance = new IndexingService();
    }
    return IndexingService.instance;
  }

  async startIndexing(force = false): Promise<void> {
    const now = Date.now();
    
    if (this.isIndexing && !force) {
      console.log('Indexing already in progress');
      return;
    }

    if (!force && (now - this.lastIndexCheckedTime) < this.minTimeBetweenIndexing) {
      console.log('Indexing skipped - too soon since last check');
      return;
    }

    this.isIndexing = true;
    this.lastIndexCheckedTime = now;
    this.processedFiles = 0;

    try {
      console.log('🚀 Starting manual full indexing...');

      // Manual mode: only do full indexing when explicitly requested
      await this.quickScan();
      await this.indexDirectory(this.basePath);

      console.log('✅ Manual indexing completed successfully');
    } catch (error) {
      console.error('❌ Error during indexing:', error);
    } finally {
      this.isIndexing = false;
    }
  }

  async fastIndexRecentFiles(daysBack: number = 30, useSmartRefresh: boolean = false): Promise<{ newAssets: number; updatedAssets: number; deletedAssets: number; foldersChecked: number } | void> {
    if (this.isFastIndexing) {
      console.log('Fast indexing already in progress');
      return;
    }

    this.isFastIndexing = true;

    try {
      if (useSmartRefresh) {
        console.log(`🚀 Smart refresh with folder tracking - checking files from last ${daysBack} days only...`);
        const results = await this.smartRefreshWithFolderTracking(daysBack);
        console.log(`✅ Smart refresh completed: ${results.newAssets} new, ${results.updatedAssets} updated, ${results.deletedAssets} deleted from ${results.foldersChecked} folders`);
        return results;
      }

      // Original fast indexing logic for backward compatibility
      console.log(`🚀 Fast indexing - finding newest folders and files dynamically...`);

      // Dynamically find the newest folders by scanning year directories
      const newestFolders = await this.findNewestFolders(this.basePath, 10); // Get top 10 newest folders

      const newestFiles: Array<{fullPath: string, mtime: number}> = [];

      for (const folder of newestFolders) {
        try {
          console.log(`📁 Scanning newest folder: ${folder.path} (modified: ${new Date(folder.mtime).toISOString()})`);
          const folderFiles = await this.findAllFiles(folder.path);
          newestFiles.push(...folderFiles);
          console.log(`Found ${folderFiles.length} files in ${path.basename(folder.path)}`);
        } catch (error) {
          console.warn(`Could not scan folder ${folder.path}:`, error);
        }
      }
      
      // Sort by modification time (newest first)
      newestFiles.sort((a, b) => b.mtime - a.mtime);
      
      // Take only the newest 500 files to avoid overwhelming the system
      const filesToProcess = newestFiles.slice(0, 500);

      console.log(`Found ${newestFiles.length} total files in newest folders, processing newest ${filesToProcess.length} files`);

      // Initialize progress tracking for fast indexing
      this.processedFiles = 0;
      this.totalFiles = filesToProcess.length;

      // Show first few files for debugging
      if (filesToProcess.length > 0) {
        console.log(`🔍 First 5 files to process:`);
        filesToProcess.slice(0, 5).forEach((file, i) => {
          const fileName = path.basename(file.fullPath);
          const modDate = new Date(file.mtime).toISOString();
          console.log(`   ${i + 1}. ${fileName} (${modDate})`);
        });
      }

      // Process newest files first
      for (let i = 0; i < filesToProcess.length; i++) {
        const file = filesToProcess[i];
        console.log(`🔄 Processing file ${i + 1}/${filesToProcess.length}: ${path.basename(file.fullPath)}`);
        await this.indexFile(file.fullPath);
        this.processedFiles = i + 1; // Update progress counter

        if (i % 10 === 0 && i > 0) {
          console.log(`Fast indexed ${i}/${filesToProcess.length} newest files`);
        }
      }

      console.log(`📊 Total files processed: ${this.processedFiles}`);

      // Clean up deleted files from the database (only for non-smart refresh)
      const cleanupResults = await this.cleanupDeletedFiles();
      console.log(`🧹 Cleanup completed: ${cleanupResults.deletedCount} deleted, ${cleanupResults.checkedCount} checked`);

      console.log(`✅ Fast indexing completed - processed ${this.processedFiles} newest files`);
    } catch (error) {
      console.error('❌ Error during fast indexing:', error);
    } finally {
      this.isFastIndexing = false;
    }
  }

  private async findNewestFolders(basePath: string, limit: number = 10): Promise<Array<{path: string, mtime: number}>> {
    const folders: Array<{path: string, mtime: number}> = [];
    
    try {
      // Recursively find all directories and their modification times
      const scanDirectory = async (dirPath: string, depth: number = 0): Promise<void> => {
        if (depth > 4) return; // Limit recursion depth
        
        try {
          const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
          
          for (const entry of entries) {
            if (entry.isDirectory()) {
              const fullPath = path.join(dirPath, entry.name);
              try {
                const stat = await fs.promises.stat(fullPath);
                folders.push({
                  path: fullPath,
                  mtime: stat.mtime.getTime()
                });
                
                // Recursively scan subdirectories
                await scanDirectory(fullPath, depth + 1);
              } catch (error) {
                console.warn(`Could not stat directory ${fullPath}:`, error);
              }
            }
          }
        } catch (error) {
          console.warn(`Could not read directory ${dirPath}:`, error);
        }
      };
      
      await scanDirectory(basePath);
      
      // Sort by modification time (newest first) and return top folders
      folders.sort((a, b) => b.mtime - a.mtime);
      return folders.slice(0, limit);
      
    } catch (error) {
      console.error('Error finding newest folders:', error);
      return [];
    }
  }

  private async findRecentFiles(dirPath: string, cutoffTime: number): Promise<Array<{fullPath: string, mtime: number}>> {
    const recentFiles: Array<{fullPath: string, mtime: number}> = [];

    try {
      const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        if (entry.isDirectory()) {
          const subFiles = await this.findRecentFiles(fullPath, cutoffTime);
          recentFiles.push(...subFiles);
        } else if (this.isImageFile(entry.name)) {
          try {
            const stat = await fs.promises.stat(fullPath);
            if (stat.mtime.getTime() > cutoffTime) {
              recentFiles.push({
                fullPath,
                mtime: stat.mtime.getTime()
              });
            }
          } catch (error) {
            console.warn(`Error checking file ${fullPath}:`, error);
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error);
    }

    return recentFiles;
  }

  // Efficient version that only scans directories modified recently
  private async findRecentFilesEfficient(basePath: string, cutoffTime: number): Promise<Array<{fullPath: string, mtime: number}>> {
    const recentFiles: Array<{fullPath: string, mtime: number}> = [];

    // First, get all recently modified folders
    const recentFolders = await this.getRecentFolders(basePath, cutoffTime);
    console.log(`🔍 Scanning ${recentFolders.length} recently modified folders for files...`);

    // Only scan files in recently modified folders
    for (const folder of recentFolders) {
      try {
        const folderFiles = await this.getImageFilesInFolder(folder.path);
        for (const filePath of folderFiles) {
          try {
            const stat = await fs.promises.stat(filePath);
            if (stat.mtime.getTime() > cutoffTime) {
              recentFiles.push({
                fullPath: filePath,
                mtime: stat.mtime.getTime()
              });
            }
          } catch (error) {
            console.warn(`Error checking file ${filePath}:`, error);
          }
        }
      } catch (error) {
        console.warn(`Error scanning folder ${folder.path}:`, error);
      }
    }

    return recentFiles;
  }

  private async findAllFiles(dirPath: string): Promise<Array<{fullPath: string, mtime: number}>> {
    const allFiles: Array<{fullPath: string, mtime: number}> = [];
    
    try {
      const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          const subFiles = await this.findAllFiles(fullPath);
          allFiles.push(...subFiles);
        } else if (this.isImageFile(entry.name)) {
          try {
            const stat = await fs.promises.stat(fullPath);
            allFiles.push({
              fullPath,
              mtime: stat.mtime.getTime()
            });
          } catch (error) {
            console.warn(`Error checking file ${fullPath}:`, error);
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error);
    }
    
    return allFiles;
  }

  async quickScan(): Promise<void> {
    console.log('📊 Starting quick file count...');
    this.totalFiles = await this.countFiles(this.basePath);
    console.log(`Scan completed, found ${this.totalFiles} files`);
    console.log(`Total files count updated: ${this.totalFiles} files found`);
  }

  private async countFiles(dirPath: string): Promise<number> {
    let count = 0;
    
    try {
      const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          count += await this.countFiles(fullPath);
        } else if (this.isImageFile(entry.name)) {
          count++;
          if (count % 1000 === 0) {
            console.log(`Found image file: ${entry.name}`);
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error);
    }
    
    return count;
  }

  private async indexDirectory(dirPath: string): Promise<void> {
    try {
      const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await this.indexDirectory(fullPath);
        } else if (this.isImageFile(entry.name)) {
          await this.indexFile(fullPath);
          this.processedFiles++;
          
          if (this.processedFiles % 50 === 0) {
            console.log(`Processed ${this.processedFiles} of ${this.totalFiles} files (${(this.processedFiles / this.totalFiles * 100).toFixed(1)}% complete)`);
          }
        }
      }
    } catch (error) {
      console.error(`Error indexing directory ${dirPath}:`, error);
    }
  }

  /**
   * Determine the effective date for an asset using folder structure when possible
   */
  private getEffectiveAssetDate(filePath: string, fileStats: fs.Stats): Date {
    try {
      // Extract folder path (remove filename)
      const folderPath = path.dirname(filePath);

      // Try to extract date from folder structure
      const folderDate = extractDateFromFolderPath(folderPath);

      if (folderDate && folderDate.confidence === 'high') {
        const effectiveDate = folderDateToDate(folderDate);
        console.log(`📅 Using folder date for ${path.basename(filePath)}: ${effectiveDate.toISOString()} (${folderDate.source})`);
        return effectiveDate;
      } else if (folderDate && folderDate.confidence === 'medium') {
        const effectiveDate = folderDateToDate(folderDate);
        console.log(`📅 Using folder date (medium confidence) for ${path.basename(filePath)}: ${effectiveDate.toISOString()} (${folderDate.source})`);
        return effectiveDate;
      } else {
        // Fallback to file modification time
        console.log(`📅 Using file mtime for ${path.basename(filePath)}: ${new Date(fileStats.mtime).toISOString()} (file-timestamp)`);
        return new Date(fileStats.mtime);
      }
    } catch (error) {
      console.warn(`Error determining effective date for ${filePath}, using file mtime:`, error);
      return new Date(fileStats.mtime);
    }
  }

  async indexFile(filePath: string): Promise<void> {
    try {
      const stat = await fs.promises.stat(filePath);
      const filename = path.basename(filePath);
      const relativePath = path.relative(this.basePath, filePath);

      console.log(`🔍 Indexing file: ${filename} (${relativePath})`);

      // Get effective date using folder structure when possible
      const effectiveDate = this.getEffectiveAssetDate(filePath, stat);

      // Check if file already exists in database
      const existingAsset = await db.select().from(assets).where(eq(assets.filePath, relativePath)).limit(1);

      // Skip if file hasn't been modified since last index (using file mtime for this check)
      if (existingAsset.length > 0 && existingAsset[0].lastModified >= stat.mtime) {
        console.log(`⏭️  Skipping ${filename} - already indexed and not modified`);
        return;
      }

      // Extract metadata with error handling and sanitization
      let metadata = {};
      try {
        if (this.isImageFile(filename)) {
          const rawMetadata = await exifr.parse(filePath);
          if (rawMetadata) {
            // Sanitize metadata to remove null bytes and problematic characters
            metadata = sanitizeMetadata(rawMetadata);
          }
        }
      } catch (metadataError) {
        console.warn(`Failed to extract metadata from ${filePath}:`, metadataError);
        metadata = {}; // Use empty metadata if extraction fails
      }

      const asset = {
        id: existingAsset.length > 0 ? existingAsset[0].id : uuidv4(),
        filename,
        filePath: relativePath,
        fileUrl: `/api/storage/file?path=${encodeURIComponent(relativePath)}`,
        thumbnailUrl: `/api/storage/thumbnail?path=${encodeURIComponent(relativePath)}`,
        type: getFileType(filename),
        size: stat.size,
        lastModified: effectiveDate, // Use folder-based date when available
        metadata,
        createdAt: existingAsset.length > 0 ? existingAsset[0].createdAt : new Date(),
        updatedAt: new Date()
      };

      // Insert or update asset in database with error handling
      try {
        if (existingAsset.length > 0) {
          await db.update(assets).set(asset).where(eq(assets.id, existingAsset[0].id));
          console.log(`✅ Updated existing asset: ${filename}`);
        } else {
          await db.insert(assets).values(asset);
          console.log(`✅ Added new asset: ${filename}`);
        }
      } catch (dbError) {
        console.error(`Database error for file ${filePath}:`, dbError);
        // Try with empty metadata if there's still an issue
        try {
          const assetWithoutMetadata = { ...asset, metadata: {} };
          if (existingAsset.length > 0) {
            await db.update(assets).set(assetWithoutMetadata).where(eq(assets.id, existingAsset[0].id));
            console.log(`✅ Updated existing asset (no metadata): ${filename}`);
          } else {
            await db.insert(assets).values(assetWithoutMetadata);
            console.log(`✅ Added new asset (no metadata): ${filename}`);
          }
        } catch (fallbackError) {
          console.error(`Fallback database error for file ${filePath}:`, fallbackError);
        }
      }
    } catch (error) {
      console.error(`Error indexing file ${filePath}:`, error);
      // Continue processing other files even if one fails
    }
  }

  private isImageFile(filename: string): boolean {
    // SIMPLIFIED: Index ALL files except system files

    // Skip hidden files and system files
    if (filename.startsWith('.') || filename.startsWith('~')) {
      return false;
    }

    // Skip common system/temp files
    const skipFiles = ['Thumbs.db', 'Desktop.ini', '.DS_Store', 'Icon\r'];
    if (skipFiles.includes(filename)) {
      return false;
    }

    // Index everything else - images, videos, documents, anything!
    return true;
  }

  async getAssets(limit: number = 50, offset: number = 0, filters?: { year?: string; month?: string; day?: string }): Promise<Asset[]> {
    try {
      console.log('🔍 IndexingService.getAssets called with:', { limit, offset, filters });

      // For maximum performance with proper date sorting
      if (!filters || (!filters.year && !filters.month && !filters.day)) {
        console.log('🔍 No filters applied, using fast path');
        // Fast path: no filters, get assets sorted by date (newest first)
        const result = await db.select().from(assets)
          .orderBy(desc(assets.lastModified), desc(assets.createdAt)) // Sort by date, newest first
          .limit(limit)
          .offset(offset);

        console.log(`🔍 Fast path returned ${result.length} assets`);
        return result;
      }

      console.log('🔍 Filters applied, using filtered path');
      // Filtered path: only when filters are actually applied
      let query = db.select().from(assets);
      const conditions = [];

      if (filters.year) {
        const yearNum = parseInt(filters.year, 10);
        console.log(`🔍 Adding year filter: ${yearNum}`);
        conditions.push(sql`EXTRACT(YEAR FROM last_modified) = ${yearNum}`);
      }

      if (filters.month) {
        const monthNum = parseInt(filters.month, 10);
        console.log(`🔍 Adding month filter: ${monthNum}`);
        conditions.push(sql`EXTRACT(MONTH FROM last_modified) = ${monthNum}`);
      }

      if (filters.day) {
        const dayNum = parseInt(filters.day, 10);
        console.log(`🔍 Adding day filter: ${dayNum}`);
        conditions.push(sql`EXTRACT(DAY FROM last_modified) = ${dayNum}`);
      }

      if (conditions.length > 0) {
        query = query.where(and(...conditions)) as any;
      }

      const result = await query
        .orderBy(desc(assets.lastModified), desc(assets.createdAt)) // Sort by date, newest first
        .limit(limit)
        .offset(offset);

      console.log(`🔍 Filtered query returned ${result.length} assets`);
      return result;
    } catch (error) {
      console.error('❌ Error fetching assets:', error);
      return [];
    }
  }

  // Separate method for when sorting is actually needed
  async getAssetsSorted(limit: number = 50, offset: number = 0, filters?: { year?: string; month?: string; day?: string }): Promise<Asset[]> {
    try {
      let query = db.select().from(assets);
      
      // Apply date filters if provided
      if (filters) {
        const conditions = [];
        
        if (filters.year) {
          conditions.push(sql`EXTRACT(YEAR FROM last_modified) = ${filters.year}`);
        }
        
        if (filters.month) {
          conditions.push(sql`EXTRACT(MONTH FROM last_modified) = ${filters.month}`);
        }
        
        if (filters.day) {
          conditions.push(sql`EXTRACT(DAY FROM last_modified) = ${filters.day}`);
        }
        
        if (conditions.length > 0) {
          query = query.where(and(...conditions)) as any;
        }
      }

      const result = await query
        .orderBy(desc(assets.lastModified), desc(assets.createdAt))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error fetching sorted assets:', error);
      return [];
    }
  }

  async getLatestAssets(limit: number = 50, offset: number = 0, filters?: { year?: string; month?: string; day?: string }): Promise<Asset[]> {
    try {
      // Skip automatic indexing to prevent blocking page loads
      // File watcher will handle new files automatically
      console.log('🔄 Getting latest assets - sorted by date, newest first');
      
      // Always sort by date to ensure latest files are on top
      const result = await db.select().from(assets)
        .orderBy(desc(assets.lastModified), desc(assets.createdAt), desc(assets.id))
        .limit(limit)
        .offset(offset);
      
      return result;
    } catch (error) {
      console.error('Error in getLatestAssets:', error);
      // Fallback to regular getAssets which now also sorts by date
      return this.getAssets(limit, offset, filters);
    }
  }

  async getTotalCount(filters?: { year?: string; month?: string; day?: string }): Promise<number> {
    try {
      console.log('🔢 IndexingService.getTotalCount called with:', filters);

      // For fast loading, just return approximate count if no filters
      if (!filters || (!filters.year && !filters.month && !filters.day)) {
        console.log('🔢 No filters, getting total count');
        // Simple count without filters - much faster
        const result = await db.select({ count: sql<number>`count(*)` }).from(assets);
        const totalCount = result[0]?.count || 0;
        console.log(`🔢 Total count (no filters): ${totalCount}`);
        return totalCount;
      }

      console.log('🔢 Filters applied, getting filtered count');
      // Only do expensive date extraction queries when filters are actually applied
      let query = db.select({ count: sql<number>`count(*)` }).from(assets);

      // Apply same filters as getAssets
      const conditions = [];

      if (filters.year) {
        const yearNum = parseInt(filters.year, 10);
        console.log(`🔢 Adding year filter: ${yearNum}`);
        conditions.push(sql`EXTRACT(YEAR FROM last_modified) = ${yearNum}`);
      }

      if (filters.month) {
        const monthNum = parseInt(filters.month, 10);
        console.log(`🔢 Adding month filter: ${monthNum}`);
        conditions.push(sql`EXTRACT(MONTH FROM last_modified) = ${monthNum}`);
      }

      if (filters.day) {
        const dayNum = parseInt(filters.day, 10);
        console.log(`🔢 Adding day filter: ${dayNum}`);
        conditions.push(sql`EXTRACT(DAY FROM last_modified) = ${dayNum}`);
      }

      if (conditions.length > 0) {
        query = query.where(and(...conditions)) as any;
      }

      const result = await query;
      const filteredCount = result[0]?.count || 0;
      console.log(`🔢 Filtered count: ${filteredCount}`);
      return filteredCount;
    } catch (error) {
      console.error('❌ Error getting total count:', error);
      return 0;
    }
  }

  async getAvailableYears(): Promise<string[]> {
    try {
      const result = await db.select({
        year: sql<string>`EXTRACT(YEAR FROM last_modified)::text`
      }).from(assets)
        .groupBy(sql`EXTRACT(YEAR FROM last_modified)`)
        .orderBy(sql`EXTRACT(YEAR FROM last_modified) DESC`);
      
      return result.map(row => row.year);
    } catch (error) {
      console.error('Error fetching available years:', error);
      return [];
    }
  }

  getIndexingStatus() {
    return {
      isIndexing: this.isIndexing,
      isFastIndexing: this.isFastIndexing,
      current: this.processedFiles,
      total: this.totalFiles,
      percentage: this.totalFiles > 0 ? (this.processedFiles / this.totalFiles * 100) : 0,
      phase: this.isFastIndexing ? 'recent-refresh' : (this.isIndexing ? 'full-indexing' : 'idle'),
      message: this.isFastIndexing ? 'Checking recent folders and files for changes...' :
               (this.isIndexing ? 'Full indexing in progress...' : 'Ready'),
      // Enhanced progress information for modal display
      currentOperation: this.currentOperation,
      currentFolder: this.currentFolder,
      foldersToCheck: this.foldersToCheck,
      foldersChecked: this.foldersChecked,
      detectedChanges: this.detectedChanges
    };
  }

  async searchAssets(query: string, dateFilters?: { year?: string; month?: string; day?: string }): Promise<Asset[]> {
    const searchStartTime = Date.now();
    const SEARCH_TIMEOUT = 10000; // 10 second timeout for search

    try {
      if (!query || !query.trim()) {
        return [];
      }

      console.log('🔍 ⚡ OPTIMIZED Advanced search query:', query);

      // ⚡ SMART SEARCH: Handle multiple terms properly
      console.log('🔍 Using smart search (filename + metadata)');

      // Split query into individual terms for better matching
      const searchTerms = query.toLowerCase().trim().split(/\s+/);
      console.log('🔍 Search terms:', searchTerms);

      // Build date conditions
      const dateConditions = [];
      if (dateFilters?.year) {
        const yearNum = parseInt(dateFilters.year, 10);
        console.log(`🗓️ Adding YEAR filter: ${yearNum}`);
        dateConditions.push(sql`EXTRACT(YEAR FROM last_modified) = ${yearNum}`);
      }
      if (dateFilters?.month) {
        const monthNum = parseInt(dateFilters.month, 10);
        console.log(`🗓️ Adding MONTH filter: ${monthNum}`);
        dateConditions.push(sql`EXTRACT(MONTH FROM last_modified) = ${monthNum}`);
      }
      if (dateFilters?.day) {
        const dayNum = parseInt(dateFilters.day, 10);
        console.log(`🗓️ Adding DAY filter: ${dayNum}`);
        dateConditions.push(sql`EXTRACT(DAY FROM last_modified) = ${dayNum}`);
      }

      console.log(`🗓️ Total date conditions: ${dateConditions.length}`);

      // Create search conditions for each term
      const termConditions = searchTerms.map(term => {
        const searchPattern = `%${term}%`;
        return or(
          // Search in filename
          sql`LOWER(${assets.filename}) LIKE ${searchPattern}`,
          // Search in metadata (EXIF data) - only if metadata exists
          and(
            sql`${assets.metadata} IS NOT NULL`,
            sql`LOWER(${assets.metadata}::text) LIKE ${searchPattern}`
          )
        );
      });

      // For multiple terms, require ALL terms to be found (AND logic)
      let whereCondition;
      if (termConditions.length === 1) {
        whereCondition = termConditions[0];
      } else {
        whereCondition = and(...termConditions);
      }

      // Add date filters if specified
      if (dateConditions.length > 0) {
        whereCondition = and(whereCondition, ...dateConditions);
      }

      const result = await db.select()
        .from(assets)
        .where(whereCondition)
        .orderBy(desc(assets.lastModified), desc(assets.createdAt), desc(assets.id))
        .limit(1000); // Increased limit to show more results

      console.log(`✅ Smart search found ${result.length} results for "${query}" (${searchTerms.length} terms)`);
      return result;

      // This code is unreachable - search completed above
    } catch (error) {
      console.error('❌ Error in advanced search:', error);

      // Fallback to simple filename search if advanced search fails
      try {
        console.log('🔄 Falling back to simple search...');
        const searchTerm = `%${query.toLowerCase()}%`;
        const result = await db.select().from(assets)
          .where(sql`LOWER(filename) LIKE ${searchTerm}`)
          .orderBy(desc(assets.lastModified), desc(assets.createdAt), desc(assets.id))
          .limit(100);

        console.log(`📋 Fallback search found ${result.length} assets`);
        return result;
      } catch (fallbackError) {
        console.error('❌ Fallback search also failed:', fallbackError);
        return [];
      }
    }
  }

  // ⚡ FAST filename + metadata search for simple queries (much faster than complex search with tags)
  private async fastFilenameSearch(query: string, dateFilters?: { year?: string; month?: string; day?: string }): Promise<Asset[]> {
    try {
      const searchTerm = `%${query.toLowerCase()}%`;

      // Build date conditions
      const dateConditions = [];
      if (dateFilters?.year) {
        const yearNum = parseInt(dateFilters.year, 10);
        dateConditions.push(sql`EXTRACT(YEAR FROM last_modified) = ${yearNum}`);
      }
      if (dateFilters?.month) {
        const monthNum = parseInt(dateFilters.month, 10);
        dateConditions.push(sql`EXTRACT(MONTH FROM last_modified) = ${monthNum}`);
      }
      if (dateFilters?.day) {
        const dayNum = parseInt(dateFilters.day, 10);
        dateConditions.push(sql`EXTRACT(DAY FROM last_modified) = ${dayNum}`);
      }

      // ✅ IMPROVED: Search both filename AND metadata (EXIF data)
      let whereCondition = or(
        // Search in filename
        sql`LOWER(${assets.filename}) LIKE ${searchTerm}`,
        // Search in metadata (EXIF data) - only if metadata exists
        and(
          sql`${assets.metadata} IS NOT NULL`,
          sql`LOWER(${assets.metadata}::text) LIKE ${searchTerm}`
        )
      );

      // Add date filters if specified
      if (dateConditions.length > 0) {
        whereCondition = and(whereCondition, ...dateConditions);
      }

      const result = await db.select()
        .from(assets)
        .where(whereCondition)
        .orderBy(desc(assets.lastModified), desc(assets.createdAt), desc(assets.id))
        .limit(100);

      // 🎯 GOOGLE-STYLE RANKING: Apply relevance scoring
      const rankedResults = this.applyGoogleStyleRanking(result, query);

      console.log(`⚡ Fast search (filename + metadata) found ${result.length} results, ranked by relevance`);
      return rankedResults;
    } catch (error) {
      console.error('❌ Error in fast filename + metadata search:', error);
      return [];
    }
  }

  // 🔤 GOOGLE-STYLE FUZZY SEARCH: Handle common typos and variations
  private async tryFuzzySearch(query: string, dateFilters?: { year?: string; month?: string; day?: string }): Promise<Asset[]> {
    const commonTypos = {
      // Camera brands
      'canno': 'canon', 'cannon': 'canon', 'canan': 'canon',
      'nikkon': 'nikon', 'nikon': 'nikon', 'nikn': 'nikon',
      'sony': 'sony', 'soni': 'sony',
      'fuji': 'fujifilm', 'fujifilm': 'fujifilm',

      // Common photo terms
      'lanscape': 'landscape', 'landscap': 'landscape',
      'portait': 'portrait', 'portriat': 'portrait',
      'sunet': 'sunset', 'sunst': 'sunset',
      'sunris': 'sunrise', 'sunrize': 'sunrise',

      // File types
      'jpg': 'jpeg', 'jpeg': 'jpeg',
      'tif': 'tiff', 'tiff': 'tiff',
    };

    const queryLower = query.toLowerCase();

    // Try exact typo corrections first
    if (commonTypos[queryLower]) {
      console.log(`🔤 Trying typo correction: "${query}" → "${commonTypos[queryLower]}"`);
      return await this.fastFilenameSearch(commonTypos[queryLower], dateFilters);
    }

    // Try partial matches (remove last character for incomplete words)
    if (query.length > 3) {
      const partial = query.slice(0, -1);
      console.log(`🔤 Trying partial match: "${query}" → "${partial}"`);
      const partialResult = await this.fastFilenameSearch(partial, dateFilters);
      if (partialResult.length > 0) {
        return partialResult;
      }
    }

    return [];
  }

  // 🎯 GOOGLE-STYLE RANKING: Apply relevance scoring like Google Search
  private applyGoogleStyleRanking(results: Asset[], query: string): Asset[] {
    const queryLower = query.toLowerCase().trim();

    return results
      .map(asset => {
        let score = 0;
        const filename = asset.filename.toLowerCase();
        const metadata = asset.metadata ? JSON.stringify(asset.metadata).toLowerCase() : '';

        // 🎯 EXACT MATCH BONUS (like Google's exact match preference)
        if (filename === queryLower) score += 1000;
        if (filename.includes(queryLower)) score += 500;

        // 🎯 FILENAME POSITION BONUS (earlier in filename = more relevant)
        const filenameIndex = filename.indexOf(queryLower);
        if (filenameIndex === 0) score += 300; // Starts with query
        else if (filenameIndex > 0) score += 200 - filenameIndex; // Earlier = better

        // 🎯 METADATA RELEVANCE (EXIF data matches)
        if (metadata.includes(queryLower)) score += 100;

        // 🎯 RECENCY BONUS (newer photos rank higher)
        const daysSinceModified = (Date.now() - new Date(asset.lastModified).getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceModified < 30) score += 50; // Recent photos boost
        else if (daysSinceModified < 365) score += 25; // This year boost

        // 🎯 FILE SIZE BONUS (larger files often higher quality)
        if (asset.size > 5 * 1024 * 1024) score += 20; // >5MB bonus
        else if (asset.size > 1 * 1024 * 1024) score += 10; // >1MB bonus

        // 🎯 FILE TYPE BONUS (prioritize common photo formats)
        const ext = asset.filename.split('.').pop()?.toLowerCase();
        if (['jpg', 'jpeg'].includes(ext || '')) score += 15;
        else if (['png', 'webp'].includes(ext || '')) score += 10;
        else if (['raw', 'cr2', 'nef'].includes(ext || '')) score += 25; // RAW files are high quality

        return { ...asset, relevanceScore: score };
      })
      .sort((a, b) => {
        // Primary sort: Relevance score (descending)
        if (b.relevanceScore !== a.relevanceScore) {
          return b.relevanceScore - a.relevanceScore;
        }
        // Secondary sort: Last modified (descending)
        return new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime();
      })
      .map(({ relevanceScore, ...asset }) => asset); // Remove score from final result
  }

  /**
   * Re-index existing assets with folder-based dates
   * This will update the lastModified field for all assets to use folder structure dates
   */
  async updateExistingAssetsWithFolderDates(limit: number = 1000): Promise<void> {
    console.log('🔄 Starting folder-based date update for existing assets...');

    try {
      let offset = 0;
      let updatedCount = 0;
      let totalProcessed = 0;

      while (true) {
        // Get batch of assets
        const assetBatch = await db.select().from(assets)
          .limit(limit)
          .offset(offset);

        if (assetBatch.length === 0) {
          break; // No more assets to process
        }

        console.log(`Processing batch ${Math.floor(offset / limit) + 1}: ${assetBatch.length} assets`);

        for (const asset of assetBatch) {
          try {
            const fullPath = path.join(this.basePath, asset.filePath);

            // Check if file still exists
            if (!fs.existsSync(fullPath)) {
              console.log(`⚠️  File no longer exists: ${asset.filePath}`);
              continue;
            }

            const stat = await fs.promises.stat(fullPath);
            const effectiveDate = this.getEffectiveAssetDate(fullPath, stat);

            // Only update if the effective date is different from current lastModified
            const currentDate = new Date(asset.lastModified);
            if (Math.abs(effectiveDate.getTime() - currentDate.getTime()) > 1000) { // More than 1 second difference
              await db.update(assets)
                .set({
                  lastModified: effectiveDate,
                  updatedAt: new Date()
                })
                .where(eq(assets.id, asset.id));

              updatedCount++;

              if (updatedCount % 50 === 0) {
                console.log(`📅 Updated ${updatedCount} assets with folder dates`);
              }
            }

            totalProcessed++;
          } catch (error) {
            console.warn(`Error updating asset ${asset.filePath}:`, error);
          }
        }

        offset += limit;
      }

      console.log(`✅ Folder date update completed: ${updatedCount} assets updated out of ${totalProcessed} processed`);
    } catch (error) {
      console.error('❌ Error updating assets with folder dates:', error);
    }
  }

  // NEW: Clean up deleted files from database (optimized for large databases)
  private async cleanupDeletedFiles(): Promise<{ deletedCount: number; checkedCount: number }> {
    try {
      console.log('🧹 Cleaning up deleted files from database...');

      // For large databases (>10k assets), only check recent assets to avoid timeout
      const totalAssets = await db.select({ count: sql`count(*)` }).from(assets);
      const assetCount = Number(totalAssets[0].count);

      let assetsToCheck;
      let checkedCount = 0;

      if (assetCount > 10000) {
        console.log(`📊 Large database detected (${assetCount} assets). Checking only recent 2000 assets for performance.`);
        // Check more recent assets for better coverage
        assetsToCheck = await db.select({
          id: assets.id,
          filePath: assets.filePath,
          filename: assets.filename
        }).from(assets)
          .orderBy(desc(assets.lastModified))
          .limit(2000);
      } else {
        console.log(`📊 Checking all ${assetCount} assets for deleted files.`);
        assetsToCheck = await db.select({
          id: assets.id,
          filePath: assets.filePath,
          filename: assets.filename
        }).from(assets);
      }

      let deletedCount = 0;
      checkedCount = assetsToCheck.length;

      for (const asset of assetsToCheck) {
        const fullPath = path.join(this.basePath, asset.filePath);

        try {
          // Check if file still exists
          await fs.promises.access(fullPath);
        } catch (error) {
          // File doesn't exist, remove from database
          try {
            await db.delete(assets).where(eq(assets.id, asset.id));
            deletedCount++;
            console.log(`🗑️  Removed deleted file from database: ${asset.filename}`);
          } catch (dbError) {
            console.error(`❌ Error removing asset ${asset.filename} from database:`, dbError);
          }
        }
      }

      if (deletedCount > 0) {
        console.log(`✅ Cleaned up ${deletedCount} deleted files from database (checked ${checkedCount} assets)`);
      } else {
        console.log(`✅ No deleted files found - database is clean (checked ${checkedCount} assets)`);
      }

      return { deletedCount, checkedCount };
    } catch (error) {
      console.error('❌ Error during deleted file cleanup:', error);
      return { deletedCount: 0, checkedCount: 0 };
    }
  }

  // NEW: Force reset indexing states (for debugging stuck states)
  forceResetIndexingState(): void {
    console.log('🔄 Force resetting indexing states...');
    this.isIndexing = false;
    this.isFastIndexing = false;
    console.log('✅ Indexing states reset');
  }

  // NEW: Force cleanup deleted files (public method)
  async forceCleanupDeletedFiles(): Promise<{ deletedCount: number; checkedCount: number }> {
    console.log('🧹 Force cleanup: Starting deleted file cleanup...');
    const results = await this.cleanupDeletedFiles();
    console.log(`✅ Force cleanup: Completed - ${results.deletedCount} deleted, ${results.checkedCount} checked`);
    return results;
  }

  // NEW: Quick cleanup check - only checks a sample of recent assets for performance
  async quickCleanupCheck(): Promise<{ deletedCount: number; checkedCount: number }> {
    console.log('🧹 Quick cleanup: Starting sample cleanup check...');

    try {
      // Only check the most recent 100 assets to keep it fast
      const recentAssets = await db.select({
        id: assets.id,
        filePath: assets.filePath,
        filename: assets.filename
      }).from(assets)
        .orderBy(desc(assets.createdAt))
        .limit(100);

      console.log(`🔍 Quick cleanup: Checking ${recentAssets.length} recent assets...`);

      let deletedCount = 0;
      let checkedCount = 0;

      for (const asset of recentAssets) {
        const fullPath = path.join(this.basePath, asset.filePath);
        checkedCount++;

        try {
          await fs.promises.access(fullPath);
          // File exists, continue
        } catch (error) {
          // File doesn't exist, remove from database
          try {
            await db.delete(assets).where(eq(assets.id, asset.id));
            deletedCount++;
            console.log(`🗑️  Quick cleanup: Removed deleted file ${asset.filename}`);
          } catch (dbError) {
            console.error(`❌ Quick cleanup: Error removing ${asset.filename}:`, dbError);
          }
        }
      }

      console.log(`✅ Quick cleanup completed: checked ${checkedCount} assets, removed ${deletedCount} deleted files`);
      return { deletedCount, checkedCount };

    } catch (error) {
      console.error('❌ Error during quick cleanup:', error);
      return { deletedCount: 0, checkedCount: 0 };
    }
  }

  // NEW: Comprehensive cleanup that checks ALL assets and returns detailed results
  async comprehensiveCleanupDeletedFiles(): Promise<{ deletedCount: number; checkedCount: number }> {
    console.log('🧹 Starting comprehensive cleanup of ALL assets...');

    try {
      // Get total count first
      const totalAssets = await db.select({ count: sql`count(*)` }).from(assets);
      const totalCount = Number(totalAssets[0].count);

      console.log(`📊 Total assets to check: ${totalCount}`);

      let deletedCount = 0;
      let checkedCount = 0;
      const batchSize = 500; // Process in batches to avoid memory issues

      // Process all assets in batches
      for (let offset = 0; offset < totalCount; offset += batchSize) {
        console.log(`🔄 Processing batch ${Math.floor(offset / batchSize) + 1}/${Math.ceil(totalCount / batchSize)} (${offset + 1}-${Math.min(offset + batchSize, totalCount)} of ${totalCount})`);

        // Get batch of assets
        const assetBatch = await db.select({
          id: assets.id,
          filePath: assets.filePath,
          filename: assets.filename
        }).from(assets)
          .limit(batchSize)
          .offset(offset);

        // Check each asset in the batch
        for (const asset of assetBatch) {
          const fullPath = path.join(this.basePath, asset.filePath);
          checkedCount++;

          try {
            // Check if file still exists
            await fs.promises.access(fullPath);
            // File exists, continue
          } catch (error) {
            // File doesn't exist, remove from database
            try {
              await db.delete(assets).where(eq(assets.id, asset.id));
              deletedCount++;
              console.log(`🗑️  Removed deleted file from database: ${asset.filename}`);
            } catch (dbError) {
              console.error(`❌ Error removing asset ${asset.filename} from database:`, dbError);
            }
          }
        }

        // Small delay between batches to prevent overwhelming the system
        if (offset + batchSize < totalCount) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log(`✅ Comprehensive cleanup completed: checked ${checkedCount} assets, removed ${deletedCount} deleted files`);

      return { deletedCount, checkedCount };

    } catch (error) {
      console.error('❌ Error during comprehensive cleanup:', error);
      throw error;
    }
  }

  // NEW: Smart refresh that only checks recent folders (much faster for daily refresh)
  async smartRecentRefresh(daysBack: number = 1): Promise<{ newAssets: number; updatedAssets: number; deletedAssets: number; foldersChecked: number }> {
    const startTime = Date.now();
    const maxDuration = 300000; // 5 minutes timeout (increased from 30 seconds)

    try {
      const cutoffTime = Date.now() - (daysBack * 24 * 60 * 60 * 1000);
      console.log(`🚀 Starting smart recent refresh - checking folders modified in last ${daysBack} days...`);
      console.log(`📅 Cutoff time: ${new Date(cutoffTime).toISOString()}`);
      console.log(`⏱️  Timeout: ${maxDuration / 1000} seconds`);

      let newAssets = 0;
      let updatedAssets = 0;
      let deletedAssets = 0;
      let foldersChecked = 0;

      // Get folders that might have changes (recently modified OR with file count mismatches)
      const allRecentFolders = await this.getRecentFolders(this.basePath, cutoffTime);
      console.log(`📁 Found ${allRecentFolders.length} folders to check for changes`);

      // Prioritize recently modified folders and limit the total number to process
      const recentlyModifiedFolders = allRecentFolders.filter(f => f.mtime >= cutoffTime);
      const countMismatchFolders = allRecentFolders.filter(f => f.mtime < cutoffTime);

      // For daily refresh, focus primarily on recently modified folders
      // Only include a few count mismatch folders to avoid overwhelming the system
      const maxFoldersToProcess = 20; // Reduced limit for better performance
      const maxCountMismatchFolders = 5; // Very limited count mismatch folders for daily refresh

      const recentFolders = [
        ...recentlyModifiedFolders.slice(0, maxFoldersToProcess), // All recent folders up to limit
        ...countMismatchFolders.slice(0, Math.min(maxCountMismatchFolders, Math.max(0, maxFoldersToProcess - recentlyModifiedFolders.length)))
      ];

      console.log(`📁 Processing ${recentFolders.length} folders (${recentlyModifiedFolders.length} recently modified, ${recentFolders.length - recentlyModifiedFolders.length} with count mismatches)`);
      console.log(`⚠️  Note: Found ${countMismatchFolders.length} folders with count mismatches - consider running a full index to fix database inconsistencies`);

      // Always do a targeted scan for recent files as a fallback
      // This ensures we catch files that might be missed due to folder timestamp issues
      console.log(`🔍 Also doing targeted scan for recent files as backup...`);
      const recentFiles = await this.findRecentFilesEfficient(this.basePath, cutoffTime);
      console.log(`📄 Found ${recentFiles.length} recent files to process directly`);

      // If we found neither folders nor files, expand the search to be more thorough
      if (recentFolders.length === 0 && recentFiles.length === 0) {
        console.log(`⚠️  No recent changes detected, expanding search to last 7 days...`);
        const expandedCutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days instead of 30
        const expandedFolders = await this.getRecentFolders(this.basePath, expandedCutoff);
        const expandedRecentFolders = expandedFolders.filter(f => f.mtime >= expandedCutoff);
        recentFolders.push(...expandedRecentFolders.slice(0, 5)); // Very limited expansion
        console.log(`📁 Expanded search found ${expandedRecentFolders.length} additional folders (processing first 5)`);
      }

      // Set up progress tracking
      const totalWork = recentFolders.length + recentFiles.length;
      this.totalFiles = Math.max(totalWork, 1); // Ensure at least 1 to avoid division by zero
      this.processedFiles = 0;

      // Check each recent folder for changes
      for (const folderInfo of recentFolders) {
        // Check timeout
        if (Date.now() - startTime > maxDuration) {
          console.log(`⏱️  Timeout reached, stopping folder checks`);
          break;
        }

        try {
          console.log(`📁 Checking recent folder: ${path.basename(folderInfo.path)} (modified: ${new Date(folderInfo.mtime).toISOString()})`);
          const changes = await this.checkFolderChanges(folderInfo);
          newAssets += changes.newFiles;
          updatedAssets += changes.updatedFiles;
          deletedAssets += changes.deletedFiles;
          foldersChecked++;
          this.processedFiles++;

          // Log progress every 10 folders (since there should be fewer)
          if (foldersChecked % 10 === 0) {
            console.log(`📊 Progress: ${foldersChecked}/${recentFolders.length} recent folders checked`);
          }

        } catch (error) {
          console.warn(`Error checking folder ${folderInfo.path}:`, error);
          this.processedFiles++; // Still count as processed
        }
      }

      // Process recent files
      for (const fileInfo of recentFiles) {
        // Check timeout
        if (Date.now() - startTime > maxDuration) {
          console.log(`⏱️  Timeout reached, stopping file processing`);
          break;
        }

        try {
          await this.indexFile(fileInfo.fullPath);
          newAssets++;
          this.processedFiles++;
        } catch (error) {
          console.warn(`Error indexing recent file ${fileInfo.fullPath}:`, error);
          this.processedFiles++; // Still count as processed
        }
      }

      console.log(`✅ Smart recent refresh completed: ${newAssets} new, ${updatedAssets} updated, ${deletedAssets} deleted from ${foldersChecked} folders`);
      return { newAssets, updatedAssets, deletedAssets, foldersChecked };
    } catch (error) {
      console.error('❌ Error during smart recent refresh:', error);
      return { newAssets: 0, updatedAssets: 0, deletedAssets: 0, foldersChecked: 0 };
    }
  }

  /**
   * NEW: Smart refresh using folder tracking system for much better performance
   */
  private async smartRefreshWithFolderTracking(daysBack: number): Promise<{ newAssets: number; updatedAssets: number; deletedAssets: number; foldersChecked: number }> {
    let newAssets = 0;
    let updatedAssets = 0;
    let deletedAssets = 0;
    let foldersChecked = 0;

    try {
      console.log(`🚀 Starting smart refresh with folder tracking (last ${daysBack} days)...`);

      // Initialize progress tracking
      this.currentOperation = 'Smart refresh with folder tracking';
      this.foldersChecked = 0;
      this.detectedChanges = [];

      // Calculate cutoff time
      const cutoffTime = new Date();
      cutoffTime.setDate(cutoffTime.getDate() - daysBack);

      // Get folders that need updating based on modification time
      const foldersToUpdate = await this.folderTracker.getFoldersNeedingUpdate(cutoffTime);
      console.log(`📁 Found ${foldersToUpdate.length} folders that need checking`);

      // Set up progress tracking
      this.foldersToCheck = foldersToUpdate.length;
      this.totalFiles = foldersToUpdate.length;

      // Process each folder using the folder tracking system
      for (const folderPath of foldersToUpdate) {
        try {
          // Update current folder being processed
          this.currentFolder = path.basename(folderPath);
          console.log(`🔍 Checking folder: ${this.currentFolder}`);

          // Update folder statistics and get change information
          const changeInfo = await this.folderTracker.updateFolderStats(folderPath);

          if (changeInfo.hasChanges) {
            console.log(`📊 Folder changes detected: +${changeInfo.newFiles} new, -${changeInfo.deletedFiles} deleted, ~${changeInfo.modifiedFiles} modified`);

            // Track detected changes for modal display
            if (changeInfo.newFiles > 0) {
              this.detectedChanges.push(`+${changeInfo.newFiles} new in ${this.currentFolder}`);
            }
            if (changeInfo.deletedFiles > 0) {
              this.detectedChanges.push(`-${changeInfo.deletedFiles} deleted in ${this.currentFolder}`);
            }
            if (changeInfo.modifiedFiles > 0) {
              this.detectedChanges.push(`~${changeInfo.modifiedFiles} updated in ${this.currentFolder}`);
            }

            // If there are changes, re-index the folder
            await this.indexFolderFiles(folderPath);

            newAssets += changeInfo.newFiles;
            deletedAssets += changeInfo.deletedFiles;
            updatedAssets += changeInfo.modifiedFiles;
          } else {
            console.log(`✅ No changes in folder: ${this.currentFolder}`);
          }

          foldersChecked++;
          this.foldersChecked = foldersChecked;
          this.processedFiles = foldersChecked; // Update progress

        } catch (error) {
          console.error(`❌ Error processing folder ${folderPath}:`, error);
          foldersChecked++;
          this.foldersChecked = foldersChecked;
          this.processedFiles = foldersChecked; // Still count as processed
        }
      }

      // Also scan for completely new folders that aren't in the database yet
      const newFolders = await this.scanForNewFolders(this.basePath, cutoffTime);
      for (const newFolderPath of newFolders) {
        try {
          console.log(`📁 Processing new folder: ${path.basename(newFolderPath)}`);

          // Create folder record and index all files
          await this.folderTracker.getOrCreateFolder(newFolderPath);
          const newFolderFiles = await this.indexFolderFiles(newFolderPath);

          newAssets += newFolderFiles;
          foldersChecked++;

        } catch (error) {
          console.error(`❌ Error processing new folder ${newFolderPath}:`, error);
        }
      }

      // Clean up orphaned assets (assets in database but files no longer exist)
      this.currentOperation = 'Cleaning up orphaned assets';
      this.currentFolder = '';
      console.log('🔄 About to run orphaned asset cleanup...');
      try {
        const orphanedCount = await this.cleanupOrphanedAssets();
        console.log(`🔄 Orphaned asset cleanup completed, found ${orphanedCount} orphaned assets`);
        deletedAssets += orphanedCount;
        if (orphanedCount > 0) {
          this.detectedChanges.push(`${orphanedCount} orphaned assets cleaned up`);
        }
      } catch (cleanupError) {
        console.error('❌ Error during orphaned asset cleanup:', cleanupError);
        // Continue with the refresh even if cleanup fails
      }

      console.log(`✅ Smart refresh with folder tracking completed: ${newAssets} new, ${updatedAssets} updated, ${deletedAssets} deleted from ${foldersChecked} folders`);
      return { newAssets, updatedAssets, deletedAssets, foldersChecked };

    } catch (error) {
      console.error('❌ Error during smart refresh with folder tracking:', error);
      return { newAssets: 0, updatedAssets: 0, deletedAssets: 0, foldersChecked: 0 };
    }
  }

  /**
   * Index all image files in a specific folder
   */
  private async indexFolderFiles(folderPath: string): Promise<number> {
    let indexedCount = 0;

    try {
      const imageFiles = await this.getImageFilesInFolder(folderPath);

      for (const filePath of imageFiles) {
        try {
          await this.indexFile(filePath);
          indexedCount++;
        } catch (error) {
          console.error(`❌ Error indexing file ${filePath}:`, error);
        }
      }

    } catch (error) {
      console.error(`❌ Error indexing folder ${folderPath}:`, error);
    }

    return indexedCount;
  }

  /**
   * Index a single file immediately (for uploads)
   */
  async indexSingleFile(filePath: string): Promise<void> {
    console.log(`🔍 Indexing single file: ${filePath}`);

    try {
      await this.indexFile(filePath);
      console.log(`✅ Successfully indexed: ${path.basename(filePath)}`);
    } catch (error) {
      console.error(`❌ Error indexing single file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Scan for new folders that aren't tracked in the database yet
   */
  private async scanForNewFolders(basePath: string, cutoffTime: Date): Promise<string[]> {
    const newFolders: string[] = [];

    try {
      // Get all folders from database
      const trackedFolders = await this.folderTracker.getAllFolders();
      const trackedPaths = new Set(trackedFolders.map(f => path.join(basePath, f.path)));

      // Recursively scan for folders
      const scanDirectory = async (dirPath: string, depth: number = 0): Promise<void> => {
        if (depth > 4) return; // Limit depth

        try {
          const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });

          for (const entry of entries) {
            if (entry.isDirectory()) {
              const fullPath = path.join(dirPath, entry.name);

              // Check if this folder is not tracked and has been modified recently
              if (!trackedPaths.has(fullPath)) {
                const stat = await fs.promises.stat(fullPath);
                if (stat.mtime >= cutoffTime) {
                  // Check if it contains image files
                  const imageFiles = await this.getImageFilesInFolder(fullPath);
                  if (imageFiles.length > 0) {
                    newFolders.push(fullPath);
                  }
                }
              }

              // Recursively scan subdirectories
              await scanDirectory(fullPath, depth + 1);
            }
          }
        } catch (error) {
          console.warn(`Could not scan directory ${dirPath}:`, error);
        }
      };

      await scanDirectory(basePath);

    } catch (error) {
      console.error('Error scanning for new folders:', error);
    }

    return newFolders;
  }

  // Get folders that might have changes (either recently modified OR with file count mismatches)
  private async getRecentFolders(basePath: string, cutoffTime: number): Promise<Array<{path: string, mtime: number, fileCount: number}>> {
    const folders: Array<{path: string, mtime: number, fileCount: number}> = [];

    const scanDirectory = async (dirPath: string, depth: number = 0): Promise<void> => {
      if (depth > 6) return; // Reasonable depth limit

      try {
        const stat = await fs.promises.stat(dirPath);
        const folderMtime = stat.mtime.getTime();

        const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
        let imageFileCount = 0;

        // Count image files in this directory
        for (const entry of entries) {
          if (entry.isFile() && this.isImageFile(entry.name)) {
            imageFileCount++;
          }
        }

        // If this folder has images, check if it needs to be processed
        if (imageFileCount > 0) {
          const isRecentlyModified = folderMtime >= cutoffTime;

          // For recently modified folders, include them immediately without database check
          if (isRecentlyModified) {
            console.log(`📁 Including recently modified folder: ${path.basename(dirPath)} (${imageFileCount} files)`);
            folders.push({
              path: dirPath,
              mtime: folderMtime,
              fileCount: imageFileCount
            });
          }
          // Skip database count checks for older folders to improve performance
          // The daily refresh should focus on recently modified folders only
        }

        // Always recursively scan subdirectories (they might have changes even if parent doesn't)
        for (const entry of entries) {
          if (entry.isDirectory()) {
            const fullPath = path.join(dirPath, entry.name);
            await scanDirectory(fullPath, depth + 1);
          }
        }
      } catch (error) {
        console.warn(`Could not scan directory ${dirPath}:`, error);
      }
    };

    await scanDirectory(basePath);
    return folders;
  }

  // Check if a folder has changes by comparing file count and modification time
  private async checkFolderChanges(folderInfo: {path: string, mtime: number, fileCount: number}): Promise<{newFiles: number, updatedFiles: number, deletedFiles: number}> {
    try {
      const relativePath = path.relative(this.basePath, folderInfo.path);

      // Get assets from database for this folder
      const dbAssets = await db.select().from(assets)
        .where(like(assets.filePath, `${relativePath}%`));

      const dbFileCount = dbAssets.length;
      const actualFileCount = folderInfo.fileCount;

      let newFiles = 0;
      let updatedFiles = 0;
      let deletedFiles = 0;

      // Quick check: if counts match and folder hasn't been modified recently, skip detailed check
      const folderModifiedRecently = Date.now() - folderInfo.mtime < (7 * 24 * 60 * 60 * 1000); // 7 days

      if (dbFileCount === actualFileCount && !folderModifiedRecently) {
        // Folder likely unchanged, skip detailed check
        return { newFiles: 0, updatedFiles: 0, deletedFiles: 0 };
      }

      // Detailed check needed
      if (actualFileCount > dbFileCount) {
        // Potential new files
        const actualFiles = await this.getImageFilesInFolder(folderInfo.path);
        const dbFilePaths = new Set(dbAssets.map(asset => path.basename(asset.filePath)));

        for (const file of actualFiles) {
          if (!dbFilePaths.has(path.basename(file))) {
            // New file found, index it
            await this.indexFile(file);
            newFiles++;
          }
        }
      } else if (actualFileCount < dbFileCount) {
        // Potential deleted files
        const actualFiles = await this.getImageFilesInFolder(folderInfo.path);
        const actualFileNames = new Set(actualFiles.map(file => path.basename(file)));

        for (const dbAsset of dbAssets) {
          const fileName = path.basename(dbAsset.filePath);
          if (!actualFileNames.has(fileName)) {
            // File deleted, remove from database
            await db.delete(assets).where(eq(assets.id, dbAsset.id));
            deletedFiles++;
            console.log(`🗑️  Removed deleted file: ${fileName}`);
          }
        }
      }

      // Check for updated files if folder was modified recently
      if (folderModifiedRecently && actualFileCount === dbFileCount) {
        const actualFiles = await this.getImageFilesInFolder(folderInfo.path);
        for (const file of actualFiles) {
          const stat = await fs.promises.stat(file);
          const relativePath = path.relative(this.basePath, file);
          const dbAsset = dbAssets.find(asset => asset.filePath === relativePath);

          if (dbAsset && stat.mtime.getTime() > new Date(dbAsset.updatedAt).getTime()) {
            // File was modified, re-index it
            await this.indexFile(file);
            updatedFiles++;
          }
        }
      }

      return { newFiles, updatedFiles, deletedFiles };
    } catch (error) {
      console.error(`Error checking folder changes for ${folderInfo.path}:`, error);
      return { newFiles: 0, updatedFiles: 0, deletedFiles: 0 };
    }
  }

  // Get all image files in a specific folder
  private async getImageFilesInFolder(folderPath: string): Promise<string[]> {
    try {
      const entries = await fs.promises.readdir(folderPath, { withFileTypes: true });
      const imageFiles: string[] = [];

      for (const entry of entries) {
        if (entry.isFile() && this.isImageFile(entry.name)) {
          imageFiles.push(path.join(folderPath, entry.name));
        }
      }

      return imageFiles;
    } catch (error) {
      console.error(`Error reading folder ${folderPath}:`, error);
      return [];
    }
  }

  /**
   * Clean up orphaned assets (assets in database but files no longer exist)
   * Optimized to only check a sample of assets to avoid performance issues
   */
  private async cleanupOrphanedAssets(): Promise<number> {
    let cleanedCount = 0;

    try {
      console.log('🧹 Checking for orphaned assets (sample check)...');

      // Get a sample of assets from database (limit to 1000 for performance)
      // Focus on recently added assets and assets from folders that might have changed
      const sampleAssets = await db.select({
        id: assets.id,
        filePath: assets.filePath,
        filename: assets.filename
      }).from(assets)
        .orderBy(desc(assets.updatedAt))
        .limit(1000);

      console.log(`📊 Checking ${sampleAssets.length} recent assets for orphaned files...`);

      // Check each asset to see if the file still exists
      for (const asset of sampleAssets) {
        const fullPath = path.join(this.basePath, asset.filePath);

        try {
          await fs.promises.access(fullPath);
          // File exists, continue
        } catch (error) {
          // File doesn't exist, remove from database
          await db.delete(assets).where(eq(assets.id, asset.id));
          console.log(`🗑️ Removed orphaned asset: ${asset.filename}`);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        console.log(`✅ Cleaned up ${cleanedCount} orphaned assets`);
      } else {
        console.log('✅ No orphaned assets found in sample');
      }

    } catch (error) {
      console.error('❌ Error during orphaned asset cleanup:', error);
    }

    return cleanedCount;
  }

  // NEW: Periodic cleanup to catch missed deletions
  private startPeriodicCleanup(): void {
    // Run cleanup every 30 minutes
    setInterval(async () => {
      if (!this.isIndexing && !this.isFastIndexing) {
        console.log('🕒 Running periodic cleanup check...');
        const results = await this.cleanupDeletedFiles();
        console.log(`🕒 Periodic cleanup completed: ${results.deletedCount} deleted, ${results.checkedCount} checked`);
      }
    }, 30 * 60 * 1000); // 30 minutes

    console.log('⏰ Periodic cleanup scheduled (every 30 minutes)');
  }

  // NEW: File watching functionality (DISABLED by default to prevent crashes)
  startFileWatcher(): void {
    console.log('⚠️  File watcher disabled to prevent frontend crashes');
    console.log('💡 Use the REFRESH button to detect new files instead');
    console.log('🔧 To enable file watcher, set ENABLE_FILE_WATCHER=true in environment');

    // Check if file watcher is explicitly enabled
    const enableWatcher = process.env.ENABLE_FILE_WATCHER === 'true';

    if (!enableWatcher) {
      console.log('📋 File watcher is disabled. Use manual refresh to detect new assets.');
      return;
    }

    if (this.isWatching || this.watcher) {
      console.log('File watcher already running');
      return;
    }

    if (!fs.existsSync(this.basePath)) {
      console.warn(`Storage path does not exist: ${this.basePath}`);
      return;
    }

    console.log(`🔍 Starting optimized file watcher for: ${this.basePath}`);
    
    try {
      // Optimized watcher configuration to prevent ENOSPC errors
      this.watcher = chokidar.watch(this.basePath, {
        ignored: [
          /(^|[\/\\])\../, // ignore dotfiles
          /node_modules/,
          /\.git/,
          /\.cache/,
          /tmp/,
          /temp/,
          /thumbs\.db$/i,
          /desktop\.ini$/i,
          /\.DS_Store$/,
          // Only watch specific image/video extensions to reduce watched files
          /\.(txt|log|xml|json|db|ini|cfg|conf|bak|tmp)$/i
        ],
        persistent: true,
        ignoreInitial: true, // Don't trigger on startup for existing files
        followSymlinks: false,
        depth: 6, // Reduced depth to avoid performance issues
        usePolling: false, // Use native events, more efficient
        interval: 1000, // Only used if polling is enabled
        binaryInterval: 3000,
        awaitWriteFinish: {
          stabilityThreshold: 3000, // Wait 3 seconds for file to stabilize
          pollInterval: 200
        },
        atomic: true, // Treat atomic writes correctly
        // Limit number of concurrent stat calls
        alwaysStat: false
      });

      this.watcher
        .on('add', (filePath) => this.handleFileAdd(filePath))
        .on('change', (filePath) => this.handleFileChange(filePath))
        .on('unlink', (filePath) => this.handleFileDelete(filePath))
        .on('addDir', (dirPath) => this.handleDirAdd(dirPath))
        .on('unlinkDir', (dirPath) => this.handleDirDelete(dirPath))
        .on('error', (error) => {
          console.error('File watcher error:', error);
          // Handle ENOSPC specifically
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (errorMessage.includes('ENOSPC') || errorMessage.includes('System limit')) {
            console.warn('⚠️  File watcher system limit reached. Reducing watched scope...');
            this.stopFileWatcher();
            // Restart with more restrictive watching in 30 seconds
            setTimeout(() => {
              this.startLimitedFileWatcher();
            }, 30000);
          }
        })
        .on('ready', () => {
          console.log('✅ File monitoring active - will detect additions and deletions');
          this.isWatching = true;
        });
        
    } catch (error) {
      console.error('Error starting file watcher:', error);
      // Fallback to limited watching
      setTimeout(() => {
        this.startLimitedFileWatcher();
      }, 10000);
    }
  }

  // NEW: Limited file watcher for systems with low file descriptor limits
  private startLimitedFileWatcher(): void {
    if (this.isWatching || this.watcher) {
      return;
    }

    console.log('🔍 Starting limited file watcher (recent folders only)...');
    
    try {
      // Only watch the most recent year folders to avoid ENOSPC
      const currentYear = new Date().getFullYear();
      const watchPaths = [
        path.join(this.basePath, currentYear.toString()),
        path.join(this.basePath, (currentYear - 1).toString())
      ].filter(p => fs.existsSync(p));

      if (watchPaths.length === 0) {
        console.warn('No recent year folders found for limited watching');
        return;
      }

      this.watcher = chokidar.watch(watchPaths, {
        ignored: [
          /(^|[\/\\])\../,
          /\.(txt|log|xml|json|db|ini|cfg|conf|bak|tmp)$/i
        ],
        persistent: true,
        ignoreInitial: true,
        followSymlinks: false,
        depth: 4, // Very limited depth
        usePolling: false,
        awaitWriteFinish: {
          stabilityThreshold: 2000,
          pollInterval: 100
        },
        alwaysStat: false
      });

      this.watcher
        .on('add', (filePath) => this.handleFileAdd(filePath))
        .on('change', (filePath) => this.handleFileChange(filePath))
        .on('unlink', (filePath) => this.handleFileDelete(filePath))
        .on('error', (error) => {
          console.error('Limited file watcher error:', error);
        })
        .on('ready', () => {
          console.log('✅ Limited file watcher ready (watching recent folders only)');
          this.isWatching = true;
        });

    } catch (error) {
      console.error('Error starting limited file watcher:', error);
    }
  }

  stopFileWatcher(): void {
    if (this.watcher) {
      console.log('🔍 Stopping file watcher...');
      this.watcher.close();
      this.watcher = null;
      this.isWatching = false;
      console.log('✅ File watcher stopped');
    }
    
    if (this.watcherTimer) {
      clearTimeout(this.watcherTimer);
      this.watcherTimer = null;
    }
  }

  private handleFileAdd(filePath: string): void {
    if (this.isImageFile(path.basename(filePath))) {
      console.log(`📁 New asset file detected: ${path.basename(filePath)}`);
      this.queueFileForIndexing(filePath);
    }
  }

  private handleFileChange(filePath: string): void {
    if (this.isImageFile(path.basename(filePath))) {
      console.log(`📝 Asset file changed: ${path.basename(filePath)}`);
      this.queueFileForIndexing(filePath);
    }
  }

  private handleFileDelete(filePath: string): void {
    if (this.isImageFile(path.basename(filePath))) {
      console.log(`🗑️ Asset file deleted: ${path.basename(filePath)}`);
      this.removeFileFromDatabase(filePath);
    }
  }

  private handleDirAdd(dirPath: string): void {
    console.log(`📁 New directory detected: ${path.basename(dirPath)}`);
    // Trigger a scan of the new directory after a short delay
    setTimeout(() => {
      this.scanNewDirectory(dirPath);
    }, 5000); // Wait 5 seconds for files to settle
  }

  private handleDirDelete(dirPath: string): void {
    console.log(`🗑️ Directory deleted: ${path.basename(dirPath)}`);
    // Clean up any files from this directory in the database
    this.cleanupDirectoryFromDatabase(dirPath);
  }

  private queueFileForIndexing(filePath: string): void {
    this.watcherQueue.add(filePath);
    
    // Debounce: Process queue after 5 seconds of no new additions
    if (this.watcherTimer) {
      clearTimeout(this.watcherTimer);
    }
    
    this.watcherTimer = setTimeout(() => {
      this.processWatcherQueue();
    }, 5000);
  }

  private async processWatcherQueue(): Promise<void> {
    if (this.watcherQueue.size === 0) return;
    
    const filesToProcess = Array.from(this.watcherQueue);
    this.watcherQueue.clear();
    
    console.log(`🔄 Processing ${filesToProcess.length} files from watcher queue...`);
    
    for (const filePath of filesToProcess) {
      try {
        await this.indexFile(filePath);
        console.log(`✅ Indexed: ${path.basename(filePath)}`);
      } catch (error) {
        console.error(`❌ Error indexing ${filePath}:`, error);
      }
    }
    
    console.log(`✅ Completed processing watcher queue`);
  }

  private async removeFileFromDatabase(filePath: string): Promise<void> {
    try {
      const relativePath = path.relative(this.basePath, filePath);
      const result = await db.delete(assets).where(eq(assets.filePath, relativePath));
      console.log(`🗑️ Removed from database: ${path.basename(filePath)}`);
    } catch (error) {
      console.error(`❌ Error removing file from database: ${filePath}`, error);
    }
  }

  private async scanNewDirectory(dirPath: string): Promise<void> {
    try {
      console.log(`🔍 Scanning new directory: ${path.basename(dirPath)}`);
      const files = await this.findAllFiles(dirPath);
      
      if (files.length > 0) {
        console.log(`📁 Found ${files.length} files in new directory, indexing...`);
        
        for (const file of files) {
          await this.indexFile(file.fullPath);
        }
        
        console.log(`✅ Completed indexing new directory: ${path.basename(dirPath)}`);
      }
    } catch (error) {
      console.error(`❌ Error scanning new directory ${dirPath}:`, error);
    }
  }

  private async cleanupDirectoryFromDatabase(dirPath: string): Promise<void> {
    try {
      const relativePath = path.relative(this.basePath, dirPath);
      const result = await db.delete(assets).where(sql`file_path LIKE ${relativePath + '%'}`);
      console.log(`🗑️ Cleaned up directory from database: ${path.basename(dirPath)}`);
    } catch (error) {
      console.error(`❌ Error cleaning up directory from database: ${dirPath}`, error);
    }
  }

  // Get watcher status
  getWatcherStatus() {
    return {
      isWatching: this.isWatching,
      queueSize: this.watcherQueue.size,
      watchedPath: this.basePath
    };
  }
} 